'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import Navbar from '@/components/navigation/navbar'
import { Loading, LoadingButton } from '@/components/ui/loading'
import { MessageSquare, Plus, User, Clock } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface User {
  id: string
  name: string
  email: string
  role: string
}

interface Reply {
  id: string
  content: string
  createdAt: string
  author: User
}

interface Discussion {
  id: string
  title: string
  content: string
  createdAt: string
  author: User
  replies: Reply[]
}

export default function CourseDiscussions() {
  const { data: session } = useSession()
  const params = useParams()
  const courseId = params.id as string

  const [discussions, setDiscussions] = useState<Discussion[]>([])
  const [loading, setLoading] = useState(true)
  const [showNewDiscussion, setShowNewDiscussion] = useState(false)
  const [newDiscussion, setNewDiscussion] = useState({ title: '', content: '' })
  const [replyContent, setReplyContent] = useState<{ [key: string]: string }>({})
  const [showReplies, setShowReplies] = useState<{ [key: string]: boolean }>({})
  const [creatingDiscussion, setCreatingDiscussion] = useState(false)
  const [replyingTo, setReplyingTo] = useState<{ [key: string]: boolean }>({})

  useEffect(() => {
    if (courseId) {
      fetchDiscussions()
    }
  }, [courseId])

  const fetchDiscussions = async () => {
    try {
      const response = await fetch(`/api/discussions?courseId=${courseId}`)
      if (response.ok) {
        const data = await response.json()
        setDiscussions(data.discussions)
      }
    } catch (error) {
      console.error('Error fetching discussions:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateDiscussion = async () => {
    if (!newDiscussion.title.trim() || !newDiscussion.content.trim()) return

    setCreatingDiscussion(true)
    try {
      const response = await fetch('/api/discussions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...newDiscussion,
          courseId
        })
      })

      if (response.ok) {
        const data = await response.json()
        setDiscussions([data.discussion, ...discussions])
        setNewDiscussion({ title: '', content: '' })
        setShowNewDiscussion(false)
      }
    } catch (error) {
      console.error('Error creating discussion:', error)
    } finally {
      setCreatingDiscussion(false)
    }
  }

  const handleReply = async (discussionId: string) => {
    const content = replyContent[discussionId]?.trim()
    if (!content) return

    setReplyingTo({ ...replyingTo, [discussionId]: true })
    try {
      const response = await fetch(`/api/discussions/${discussionId}/replies`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ content })
      })

      if (response.ok) {
        const data = await response.json()
        setDiscussions(discussions.map(discussion =>
          discussion.id === discussionId
            ? { ...discussion, replies: [...discussion.replies, data.reply] }
            : discussion
        ))
        setReplyContent({ ...replyContent, [discussionId]: '' })
      }
    } catch (error) {
      console.error('Error creating reply:', error)
    } finally {
      setReplyingTo({ ...replyingTo, [discussionId]: false })
    }
  }

  const toggleReplies = (discussionId: string) => {
    setShowReplies({
      ...showReplies,
      [discussionId]: !showReplies[discussionId]
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center py-12">
            <Loading size="lg" />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Course Discussions</h1>
            <p className="text-gray-600 mt-2">Engage with fellow students and instructors</p>
          </div>
          <Button
            onClick={() => setShowNewDiscussion(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Plus className="h-4 w-4 mr-2" />
            New Discussion
          </Button>
        </div>

        <div className="space-y-6">
          {discussions.length === 0 ? (
            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardContent className="text-center py-16">
                <MessageSquare className="mx-auto h-16 w-16 text-gray-400 mb-6" />
                <h3 className="text-xl font-semibold text-gray-900 mb-3">No discussions yet</h3>
                <p className="text-gray-600 mb-8 max-w-md mx-auto">
                  Start a discussion to engage with other students and instructors. Share your thoughts, ask questions, and learn together.
                </p>
                <Button
                  onClick={() => setShowNewDiscussion(true)}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Start First Discussion
                </Button>
              </CardContent>
            </Card>
          ) : (
            discussions.map((discussion) => (
              <Card key={discussion.id} className="bg-white border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                <CardHeader className="pb-4">
                  <CardTitle className="text-xl font-semibold text-gray-900">{discussion.title}</CardTitle>
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <div className="flex items-center space-x-2">
                      <User className="h-4 w-4" />
                      <span className="font-medium">{discussion.author.name}</span>
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                        {discussion.author.role}
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="h-4 w-4" />
                      <span>{formatDistanceToNow(new Date(discussion.createdAt), { addSuffix: true })}</span>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <p className="text-gray-800 mb-6 leading-relaxed">{discussion.content}</p>

                  <div className="flex items-center justify-between mb-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleReplies(discussion.id)}
                      className="text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                    >
                      <MessageSquare className="h-4 w-4 mr-2" />
                      {discussion.replies.length} {discussion.replies.length === 1 ? 'reply' : 'replies'}
                    </Button>
                  </div>

                  {showReplies[discussion.id] && (
                    <div className="space-y-4 border-t border-gray-100 pt-4">
                      {discussion.replies.map((reply) => (
                        <div key={reply.id} className="bg-gray-50 border border-gray-200 p-4 rounded-lg ml-6">
                          <div className="flex items-center space-x-2 mb-3">
                            <span className="font-semibold text-sm text-gray-900">{reply.author.name}</span>
                            <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                              {reply.author.role}
                            </span>
                            <span className="text-xs text-gray-500">
                              {formatDistanceToNow(new Date(reply.createdAt), { addSuffix: true })}
                            </span>
                          </div>
                          <p className="text-gray-800 text-sm leading-relaxed">{reply.content}</p>
                        </div>
                      ))}

                      <div className="ml-6 flex space-x-3">
                        <Input
                          placeholder="Write a reply..."
                          value={replyContent[discussion.id] || ''}
                          onChange={(e) => setReplyContent({
                            ...replyContent,
                            [discussion.id]: e.target.value
                          })}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                              e.preventDefault()
                              handleReply(discussion.id)
                            }
                          }}
                          className="flex-1"
                        />
                        <LoadingButton
                          size="sm"
                          onClick={() => handleReply(discussion.id)}
                          disabled={!replyContent[discussion.id]?.trim()}
                          loading={replyingTo[discussion.id]}
                          loadingText="Replying..."
                          className="bg-blue-600 hover:bg-blue-700 text-white"
                        >
                          Reply
                        </LoadingButton>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {showNewDiscussion && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Start New Discussion</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Title
                    </label>
                    <Input
                      placeholder="Discussion title..."
                      value={newDiscussion.title}
                      onChange={(e) => setNewDiscussion({
                        ...newDiscussion,
                        title: e.target.value
                      })}
                      className="w-full"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Content
                    </label>
                    <textarea
                      className="w-full p-3 border border-gray-300 rounded-md text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      rows={4}
                      placeholder="What would you like to discuss?"
                      value={newDiscussion.content}
                      onChange={(e) => setNewDiscussion({
                        ...newDiscussion,
                        content: e.target.value
                      })}
                    />
                  </div>
                  <div className="flex space-x-3 pt-4">
                    <Button
                      variant="outline"
                      onClick={() => setShowNewDiscussion(false)}
                      className="flex-1"
                    >
                      Cancel
                    </Button>
                    <LoadingButton
                      onClick={handleCreateDiscussion}
                      disabled={!newDiscussion.title.trim() || !newDiscussion.content.trim()}
                      loading={creatingDiscussion}
                      loadingText="Creating..."
                      className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      Create Discussion
                    </LoadingButton>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
