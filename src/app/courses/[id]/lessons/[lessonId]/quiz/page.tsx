'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useParams, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import Navbar from '@/components/navigation/navbar'
import { Loading, LoadingButton } from '@/components/ui/loading'
import { Clock, CheckCircle, AlertCircle, ArrowLeft } from 'lucide-react'

interface Question {
  id: string
  text: string
  type: 'MULTIPLE_CHOICE' | 'TRUE_FALSE' | 'SHORT_ANSWER'
  options: string[]
  points: number
}

interface Quiz {
  id: string
  title: string
  description?: string
  timeLimit?: number
  questions: Question[]
  lesson: {
    id: string
    title: string
    module: {
      course: {
        id: string
        title: string
      }
    }
  }
}

interface Submission {
  id: string
  score: number
  submittedAt: string
  answers: Record<string, string>
}

export default function QuizPage() {
  const { data: session } = useSession()
  const params = useParams()
  const router = useRouter()
  const courseId = params.courseId as string
  const lessonId = params.lessonId as string

  const [quiz, setQuiz] = useState<Quiz | null>(null)
  const [submission, setSubmission] = useState<Submission | null>(null)
  const [answers, setAnswers] = useState<Record<string, string>>({})
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [timeLeft, setTimeLeft] = useState<number | null>(null)
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [showResults, setShowResults] = useState(false)

  useEffect(() => {
    fetchQuiz()
  }, [lessonId])

  useEffect(() => {
    if (quiz?.timeLimit && timeLeft !== null && timeLeft > 0 && !showResults) {
      const timer = setInterval(() => {
        setTimeLeft(prev => {
          if (prev && prev <= 1) {
            handleSubmit()
            return 0
          }
          return prev ? prev - 1 : 0
        })
      }, 1000)
      return () => clearInterval(timer)
    }
  }, [timeLeft, quiz, showResults])

  const fetchQuiz = async () => {
    try {
      const response = await fetch(`/api/lessons/${lessonId}/quiz`)
      if (response.ok) {
        const data = await response.json()
        setQuiz(data.quiz)
        setSubmission(data.submission)
        
        if (data.submission) {
          setShowResults(true)
          setAnswers(data.submission.answers)
        } else if (data.quiz.timeLimit) {
          setTimeLeft(data.quiz.timeLimit * 60) // Convert minutes to seconds
        }
      }
    } catch (error) {
      console.error('Error fetching quiz:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAnswerChange = (questionId: string, answer: string) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }))
  }

  const handleSubmit = async () => {
    if (submitting) return
    
    setSubmitting(true)
    try {
      const response = await fetch(`/api/lessons/${lessonId}/quiz/submit`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ answers })
      })

      if (response.ok) {
        const data = await response.json()
        setSubmission(data.submission)
        setShowResults(true)
      }
    } catch (error) {
      console.error('Error submitting quiz:', error)
    } finally {
      setSubmitting(false)
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="flex justify-center py-12">
            <Loading size="lg" />
          </div>
        </div>
      </div>
    )
  }

  if (!quiz) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="text-center">Quiz not found</div>
        </div>
      </div>
    )
  }

  if (showResults && submission) {
    const totalPoints = quiz.questions.reduce((sum, q) => sum + q.points, 0)
    const scorePercentage = Math.round((submission.score / totalPoints) * 100)

    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="mb-6">
            <Button
              variant="outline"
              onClick={() => router.push(`/courses/${courseId}/lessons/${lessonId}`)}
              className="mb-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Lesson
            </Button>
          </div>

          <Card>
            <CardHeader className="text-center">
              <CardTitle className="text-2xl">Quiz Results</CardTitle>
              <CardDescription>{quiz.title}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center mb-8">
                <div className={`text-6xl font-bold mb-2 ${getScoreColor(scorePercentage)}`}>
                  {scorePercentage}%
                </div>
                <p className="text-gray-600">
                  {submission.score} out of {totalPoints} points
                </p>
                <p className="text-sm text-gray-500 mt-2">
                  Submitted on {new Date(submission.submittedAt).toLocaleString()}
                </p>
              </div>

              <div className="space-y-6">
                {quiz.questions.map((question, index) => {
                  const userAnswer = submission.answers[question.id]
                  const isCorrect = userAnswer === question.options[0] // Assuming first option is correct
                  
                  return (
                    <div key={question.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <h3 className="font-medium">
                          Question {index + 1}: {question.text}
                        </h3>
                        <div className="flex items-center space-x-2">
                          {isCorrect ? (
                            <CheckCircle className="h-5 w-5 text-green-500" />
                          ) : (
                            <AlertCircle className="h-5 w-5 text-red-500" />
                          )}
                          <span className="text-sm text-gray-600">
                            {question.points} pts
                          </span>
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <p className="text-sm">
                          <span className="font-medium">Your answer:</span> {userAnswer || 'No answer'}
                        </p>
                        {!isCorrect && (
                          <p className="text-sm text-green-600">
                            <span className="font-medium">Correct answer:</span> {question.options[0]}
                          </p>
                        )}
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  const currentQ = quiz.questions[currentQuestion]
  const progress = ((currentQuestion + 1) / quiz.questions.length) * 100

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="mb-6">
          <Button
            variant="outline"
            onClick={() => router.push(`/courses/${courseId}/lessons/${lessonId}`)}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Lesson
          </Button>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">{quiz.title}</h1>
              <p className="text-gray-600">{quiz.description}</p>
            </div>
            {timeLeft !== null && (
              <div className="flex items-center space-x-2 text-lg font-medium">
                <Clock className="h-5 w-5" />
                <span className={timeLeft < 300 ? 'text-red-600' : 'text-gray-900'}>
                  {formatTime(timeLeft)}
                </span>
              </div>
            )}
          </div>
        </div>

        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>Question {currentQuestion + 1} of {quiz.questions.length}</span>
            <span>{Math.round(progress)}% Complete</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>
              Question {currentQuestion + 1}
              <span className="text-sm font-normal text-gray-600 ml-2">
                ({currentQ.points} {currentQ.points === 1 ? 'point' : 'points'})
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="mb-6">
              <p className="text-lg mb-4">{currentQ.text}</p>
              
              <div className="space-y-3">
                {currentQ.type === 'MULTIPLE_CHOICE' && (
                  currentQ.options.map((option, index) => (
                    <label key={index} className="flex items-center space-x-3 cursor-pointer">
                      <input
                        type="radio"
                        name={`question-${currentQ.id}`}
                        value={option}
                        checked={answers[currentQ.id] === option}
                        onChange={(e) => handleAnswerChange(currentQ.id, e.target.value)}
                        className="w-4 h-4 text-indigo-600"
                      />
                      <span>{option}</span>
                    </label>
                  ))
                )}
                
                {currentQ.type === 'TRUE_FALSE' && (
                  ['True', 'False'].map((option) => (
                    <label key={option} className="flex items-center space-x-3 cursor-pointer">
                      <input
                        type="radio"
                        name={`question-${currentQ.id}`}
                        value={option}
                        checked={answers[currentQ.id] === option}
                        onChange={(e) => handleAnswerChange(currentQ.id, e.target.value)}
                        className="w-4 h-4 text-indigo-600"
                      />
                      <span>{option}</span>
                    </label>
                  ))
                )}
                
                {currentQ.type === 'SHORT_ANSWER' && (
                  <Input
                    placeholder="Type your answer here..."
                    value={answers[currentQ.id] || ''}
                    onChange={(e) => handleAnswerChange(currentQ.id, e.target.value)}
                    className="w-full"
                  />
                )}
              </div>
            </div>

            <div className="flex justify-between">
              <Button
                variant="outline"
                onClick={() => setCurrentQuestion(prev => Math.max(0, prev - 1))}
                disabled={currentQuestion === 0}
              >
                Previous
              </Button>
              
              {currentQuestion === quiz.questions.length - 1 ? (
                <LoadingButton
                  onClick={handleSubmit}
                  disabled={!answers[currentQ.id]}
                  loading={submitting}
                  loadingText="Submitting..."
                >
                  Submit Quiz
                </LoadingButton>
              ) : (
                <Button
                  onClick={() => setCurrentQuestion(prev => prev + 1)}
                  disabled={!answers[currentQ.id]}
                >
                  Next
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
