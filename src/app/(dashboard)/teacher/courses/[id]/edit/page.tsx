'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import Navbar from '@/components/navigation/navbar'
import { Loading, LoadingButton } from '@/components/ui/loading'
import { 
  ArrowLeft, 
  Save, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Settings,
  BookOpen,
  Play,
  HelpCircle,
  GripVertical,
  Upload,
  Globe,
  Lock
} from 'lucide-react'
import Link from 'next/link'
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd'
import { LessonEditor } from '@/components/course-editor/lesson-editor'

interface Course {
  id: string
  title: string
  description: string
  category: string
  tags: string[]
  price: number
  isPaid: boolean
  isPublished: boolean
  thumbnail?: string
  modules: Module[]
}

interface Module {
  id: string
  title: string
  description?: string
  order: number
  lessons: Lesson[]
}

interface Lesson {
  id: string
  title: string
  content: string
  type: 'TEXT' | 'VIDEO' | 'QUIZ'
  order: number
  videoUrl?: string
  attachments?: string[]
}

export default function CourseEditPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const params = useParams()
  const courseId = params.id as string

  const [course, setCourse] = useState<Course | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState<'overview' | 'content' | 'settings'>('overview')

  // Course form state
  const [courseForm, setCourseForm] = useState({
    title: '',
    description: '',
    category: '',
    tags: '',
    price: '',
    isPaid: false,
    isPublished: false,
  })

  // Module and lesson management
  const [showAddModule, setShowAddModule] = useState(false)
  const [editingModule, setEditingModule] = useState<string | null>(null)
  const [showAddLesson, setShowAddLesson] = useState<string | null>(null)
  const [editingLesson, setEditingLesson] = useState<string | null>(null)

  useEffect(() => {
    if (courseId) {
      fetchCourse()
    }
  }, [courseId])

  const fetchCourse = async () => {
    try {
      const response = await fetch(`/api/courses/${courseId}`)
      if (response.ok) {
        const data = await response.json()
        setCourse(data.course)
        setCourseForm({
          title: data.course.title,
          description: data.course.description,
          category: data.course.category,
          tags: data.course.tags.join(', '),
          price: data.course.price.toString(),
          isPaid: data.course.isPaid,
          isPublished: data.course.isPublished,
        })
      } else {
        setError('Failed to load course')
      }
    } catch (error) {
      setError('An error occurred while loading the course')
    } finally {
      setLoading(false)
    }
  }

  const saveCourse = async () => {
    setSaving(true)
    setError('')

    try {
      const tags = courseForm.tags.split(',').map(tag => tag.trim()).filter(Boolean)
      const price = courseForm.isPaid ? parseFloat(courseForm.price) || 0 : 0

      const response = await fetch(`/api/courses/${courseId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: courseForm.title,
          description: courseForm.description,
          category: courseForm.category,
          tags,
          price,
          isPaid: courseForm.isPaid,
          isPublished: courseForm.isPublished,
        }),
      })

      const result = await response.json()

      if (response.ok) {
        setCourse(result.course)
        // Show success message
      } else {
        setError(result.error || 'Failed to save course')
      }
    } catch (error) {
      setError('An error occurred while saving')
    } finally {
      setSaving(false)
    }
  }

  const handleDragEnd = async (result: any) => {
    if (!result.destination) return

    const { source, destination, type } = result

    if (type === 'module') {
      // Reorder modules
      const newModules = Array.from(course!.modules)
      const [reorderedModule] = newModules.splice(source.index, 1)
      newModules.splice(destination.index, 0, reorderedModule)

      // Update order numbers
      const updatedModules = newModules.map((module, index) => ({
        ...module,
        order: index + 1
      }))

      setCourse({ ...course!, modules: updatedModules })

      // Save to backend
      try {
        await fetch(`/api/courses/${courseId}/modules/reorder`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ modules: updatedModules.map(m => ({ id: m.id, order: m.order })) })
        })
      } catch (error) {
        console.error('Failed to reorder modules:', error)
      }
    }
  }

  if (status === 'loading' || loading) {
    return <Loading />
  }

  if (!session || !['TEACHER', 'ADMIN'].includes(session.user.role)) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
          <p className="text-gray-600">You don't have permission to edit courses.</p>
        </div>
      </div>
    )
  }

  if (!course) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Course Not Found</h1>
          <p className="text-gray-600">The course you're looking for doesn't exist.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link href="/teacher/courses" className="flex items-center text-blue-600 hover:text-blue-500 mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to My Courses
          </Link>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Edit Course</h1>
              <p className="mt-2 text-gray-600">
                Manage your course content and settings
              </p>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                {course.isPublished ? (
                  <span className="flex items-center px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                    <Globe className="h-4 w-4 mr-1" />
                    Published
                  </span>
                ) : (
                  <span className="flex items-center px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium">
                    <Lock className="h-4 w-4 mr-1" />
                    Draft
                  </span>
                )}
              </div>
              
              <Link href={`/courses/${course.id}`}>
                <Button variant="outline">
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </Button>
              </Link>
              
              <Button onClick={saveCourse} disabled={saving}>
                {saving ? (
                  <>
                    <Loading size="sm" className="mr-2" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Changes
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview', icon: BookOpen },
              { id: 'content', label: 'Content', icon: Edit },
              { id: 'settings', label: 'Settings', icon: Settings },
            ].map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {tab.label}
                </button>
              )
            })}
          </nav>
        </div>

        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Course Information</CardTitle>
                <CardDescription>
                  Basic information about your course
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Course Title *
                  </label>
                  <Input
                    value={courseForm.title}
                    onChange={(e) => setCourseForm({ ...courseForm, title: e.target.value })}
                    placeholder="Enter course title"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description *
                  </label>
                  <Textarea
                    value={courseForm.description}
                    onChange={(e) => setCourseForm({ ...courseForm, description: e.target.value })}
                    placeholder="Describe what students will learn"
                    rows={4}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Category *
                    </label>
                    <select
                      value={courseForm.category}
                      onChange={(e) => setCourseForm({ ...courseForm, category: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Select a category</option>
                      <option value="Programming">Programming</option>
                      <option value="Web Development">Web Development</option>
                      <option value="Data Science">Data Science</option>
                      <option value="Design">Design</option>
                      <option value="Business">Business</option>
                      <option value="Marketing">Marketing</option>
                      <option value="Photography">Photography</option>
                      <option value="Music">Music</option>
                      <option value="Language">Language</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tags
                    </label>
                    <Input
                      value={courseForm.tags}
                      onChange={(e) => setCourseForm({ ...courseForm, tags: e.target.value })}
                      placeholder="Enter tags separated by commas"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      e.g., JavaScript, React, Frontend
                    </p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={courseForm.isPaid}
                      onChange={(e) => setCourseForm({ ...courseForm, isPaid: e.target.checked })}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label className="ml-2 block text-sm text-gray-900">
                      This is a paid course
                    </label>
                  </div>

                  {courseForm.isPaid && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Price (USD)
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        value={courseForm.price}
                        onChange={(e) => setCourseForm({ ...courseForm, price: e.target.value })}
                        placeholder="0.00"
                      />
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Course Statistics</CardTitle>
                <CardDescription>
                  Overview of your course performance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600">
                      {course.modules.length}
                    </div>
                    <div className="text-sm text-gray-600">Modules</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">
                      {course.modules.reduce((acc, module) => acc + module.lessons.length, 0)}
                    </div>
                    <div className="text-sm text-gray-600">Lessons</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-purple-600">0</div>
                    <div className="text-sm text-gray-600">Students Enrolled</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'content' && (
          <ContentManagement
            course={course}
            setCourse={setCourse}
            courseId={courseId}
            handleDragEnd={handleDragEnd}
          />
        )}

        {activeTab === 'settings' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Publishing Settings</CardTitle>
                <CardDescription>
                  Control who can see and access your course
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium">Publish Course</h3>
                    <p className="text-sm text-gray-600">
                      Make your course visible to students
                    </p>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={courseForm.isPublished}
                      onChange={(e) => setCourseForm({ ...courseForm, isPublished: e.target.checked })}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Danger Zone</CardTitle>
                <CardDescription>
                  Irreversible actions for this course
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border border-red-200 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-red-900 mb-2">Delete Course</h3>
                  <p className="text-sm text-red-700 mb-4">
                    Once you delete a course, there is no going back. Please be certain.
                  </p>
                  <Button variant="destructive">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Course
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}

// Content Management Component
function ContentManagement({ course, setCourse, courseId, handleDragEnd }: {
  course: Course
  setCourse: (course: Course) => void
  courseId: string
  handleDragEnd: (result: any) => void
}) {
  const [showAddModule, setShowAddModule] = useState(false)
  const [newModuleTitle, setNewModuleTitle] = useState('')
  const [newModuleDescription, setNewModuleDescription] = useState('')
  const [addingModule, setAddingModule] = useState(false)

  const addModule = async () => {
    if (!newModuleTitle.trim()) return

    setAddingModule(true)
    try {
      const response = await fetch(`/api/courses/${courseId}/modules`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: newModuleTitle,
          description: newModuleDescription,
          order: course.modules.length + 1
        })
      })

      if (response.ok) {
        const result = await response.json()
        setCourse({
          ...course,
          modules: [...course.modules, result.module]
        })
        setNewModuleTitle('')
        setNewModuleDescription('')
        setShowAddModule(false)
      }
    } catch (error) {
      console.error('Failed to add module:', error)
    } finally {
      setAddingModule(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Course Content</h2>
          <p className="text-gray-600">Organize your course into modules and lessons</p>
        </div>
        <Button onClick={() => setShowAddModule(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Module
        </Button>
      </div>

      {/* Add Module Form */}
      {showAddModule && (
        <Card>
          <CardHeader>
            <CardTitle>Add New Module</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Module Title *
              </label>
              <Input
                value={newModuleTitle}
                onChange={(e) => setNewModuleTitle(e.target.value)}
                placeholder="Enter module title"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <Textarea
                value={newModuleDescription}
                onChange={(e) => setNewModuleDescription(e.target.value)}
                placeholder="Describe what this module covers"
                rows={3}
              />
            </div>
            <div className="flex space-x-3">
              <Button onClick={addModule} disabled={addingModule || !newModuleTitle.trim()}>
                {addingModule ? 'Adding...' : 'Add Module'}
              </Button>
              <Button variant="outline" onClick={() => setShowAddModule(false)}>
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Modules List */}
      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="modules" type="module">
          {(provided) => (
            <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-4">
              {course.modules.map((module, index) => (
                <ModuleCard
                  key={module.id}
                  module={module}
                  index={index}
                  courseId={courseId}
                  course={course}
                  setCourse={setCourse}
                />
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      {course.modules.length === 0 && !showAddModule && (
        <Card>
          <CardContent className="text-center py-12">
            <BookOpen className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No modules yet</h3>
            <p className="text-gray-600 mb-6">
              Start building your course by adding your first module.
            </p>
            <Button onClick={() => setShowAddModule(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Your First Module
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// Module Card Component
function ModuleCard({ module, index, courseId, course, setCourse }: {
  module: Module
  index: number
  courseId: string
  course: Course
  setCourse: (course: Course) => void
}) {
  const [isEditing, setIsEditing] = useState(false)
  const [editTitle, setEditTitle] = useState(module.title)
  const [editDescription, setEditDescription] = useState(module.description || '')
  const [showAddLesson, setShowAddLesson] = useState(false)
  const [expanded, setExpanded] = useState(true)

  const updateModule = async () => {
    try {
      const response = await fetch(`/api/modules/${module.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: editTitle,
          description: editDescription
        })
      })

      if (response.ok) {
        const updatedModules = course.modules.map(m =>
          m.id === module.id
            ? { ...m, title: editTitle, description: editDescription }
            : m
        )
        setCourse({ ...course, modules: updatedModules })
        setIsEditing(false)
      }
    } catch (error) {
      console.error('Failed to update module:', error)
    }
  }

  const deleteModule = async () => {
    if (!confirm('Are you sure you want to delete this module? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/modules/${module.id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        const updatedModules = course.modules.filter(m => m.id !== module.id)
        setCourse({ ...course, modules: updatedModules })
      }
    } catch (error) {
      console.error('Failed to delete module:', error)
    }
  }

  return (
    <Draggable draggableId={module.id} index={index}>
      {(provided, snapshot) => (
        <Card
          ref={provided.innerRef}
          {...provided.draggableProps}
          className={`${snapshot.isDragging ? 'shadow-lg' : ''}`}
        >
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3 flex-1">
                <div {...provided.dragHandleProps} className="cursor-grab">
                  <GripVertical className="h-5 w-5 text-gray-400" />
                </div>

                {isEditing ? (
                  <div className="flex-1 space-y-2">
                    <Input
                      value={editTitle}
                      onChange={(e) => setEditTitle(e.target.value)}
                      placeholder="Module title"
                    />
                    <Textarea
                      value={editDescription}
                      onChange={(e) => setEditDescription(e.target.value)}
                      placeholder="Module description"
                      rows={2}
                    />
                    <div className="flex space-x-2">
                      <Button size="sm" onClick={updateModule}>Save</Button>
                      <Button size="sm" variant="outline" onClick={() => setIsEditing(false)}>
                        Cancel
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="text-lg font-semibold">
                        Module {index + 1}: {module.title}
                      </h3>
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
                        {module.lessons.length} lessons
                      </span>
                    </div>
                    {module.description && (
                      <p className="text-gray-600 mt-1">{module.description}</p>
                    )}
                  </div>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setExpanded(!expanded)}
                >
                  {expanded ? 'Collapse' : 'Expand'}
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setIsEditing(true)}
                >
                  <Edit className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={deleteModule}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>

          {expanded && (
            <CardContent className="pt-0">
              <div className="space-y-3">
                {/* Add Lesson Button */}
                <div className="flex justify-end">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowAddLesson(true)}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Lesson
                  </Button>
                </div>

                {/* Add Lesson Form */}
                {showAddLesson && (
                  <AddLessonForm
                    moduleId={module.id}
                    course={course}
                    setCourse={setCourse}
                    onCancel={() => setShowAddLesson(false)}
                  />
                )}

                {/* Lessons List */}
                <div className="space-y-2">
                  {module.lessons.map((lesson, lessonIndex) => (
                    <LessonItem
                      key={lesson.id}
                      lesson={lesson}
                      index={lessonIndex}
                      moduleId={module.id}
                      course={course}
                      setCourse={setCourse}
                    />
                  ))}
                </div>

                {module.lessons.length === 0 && !showAddLesson && (
                  <div className="text-center py-8 border-2 border-dashed border-gray-200 rounded-lg">
                    <BookOpen className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                    <p className="text-gray-500">No lessons in this module yet</p>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => setShowAddLesson(true)}
                      className="mt-2"
                    >
                      Add your first lesson
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          )}
        </Card>
      )}
    </Draggable>
  )
}

// Add Lesson Form Component
function AddLessonForm({ moduleId, course, setCourse, onCancel }: {
  moduleId: string
  course: Course
  setCourse: (course: Course) => void
  onCancel: () => void
}) {
  const [lessonData, setLessonData] = useState({
    title: '',
    content: '',
    type: 'TEXT' as 'TEXT' | 'VIDEO' | 'QUIZ',
    videoUrl: ''
  })
  const [adding, setAdding] = useState(false)

  const addLesson = async () => {
    if (!lessonData.title.trim() || !lessonData.content.trim()) return

    setAdding(true)
    try {
      const module = course.modules.find(m => m.id === moduleId)
      const order = module ? module.lessons.length + 1 : 1

      const response = await fetch(`/api/modules/${moduleId}/lessons`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...lessonData,
          order,
          videoUrl: lessonData.type === 'VIDEO' ? lessonData.videoUrl : undefined
        })
      })

      if (response.ok) {
        const result = await response.json()
        const updatedModules = course.modules.map(m =>
          m.id === moduleId
            ? { ...m, lessons: [...m.lessons, result.lesson] }
            : m
        )
        setCourse({ ...course, modules: updatedModules })
        onCancel()
      }
    } catch (error) {
      console.error('Failed to add lesson:', error)
    } finally {
      setAdding(false)
    }
  }

  return (
    <Card className="border-blue-200">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">Add New Lesson</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Lesson Title *
          </label>
          <Input
            value={lessonData.title}
            onChange={(e) => setLessonData({ ...lessonData, title: e.target.value })}
            placeholder="Enter lesson title"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Lesson Type *
          </label>
          <select
            value={lessonData.type}
            onChange={(e) => setLessonData({ ...lessonData, type: e.target.value as any })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="TEXT">Text/Reading</option>
            <option value="VIDEO">Video</option>
            <option value="QUIZ">Quiz</option>
          </select>
        </div>

        {lessonData.type === 'VIDEO' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Video URL
            </label>
            <Input
              value={lessonData.videoUrl}
              onChange={(e) => setLessonData({ ...lessonData, videoUrl: e.target.value })}
              placeholder="https://youtube.com/watch?v=..."
            />
          </div>
        )}

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Content *
          </label>
          <Textarea
            value={lessonData.content}
            onChange={(e) => setLessonData({ ...lessonData, content: e.target.value })}
            placeholder={
              lessonData.type === 'TEXT' ? 'Enter lesson content...' :
              lessonData.type === 'VIDEO' ? 'Enter video description and notes...' :
              'Enter quiz instructions...'
            }
            rows={6}
          />
        </div>

        <div className="flex space-x-3">
          <Button
            onClick={addLesson}
            disabled={adding || !lessonData.title.trim() || !lessonData.content.trim()}
          >
            {adding ? 'Adding...' : 'Add Lesson'}
          </Button>
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

// Lesson Item Component
function LessonItem({ lesson, index, moduleId, course, setCourse }: {
  lesson: Lesson
  index: number
  moduleId: string
  course: Course
  setCourse: (course: Course) => void
}) {
  const [isEditing, setIsEditing] = useState(false)

  const updateLesson = async (updatedLesson: Lesson) => {
    try {
      const response = await fetch(`/api/lessons/${lesson.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: updatedLesson.title,
          content: updatedLesson.content,
          type: updatedLesson.type,
          videoUrl: updatedLesson.videoUrl,
          attachments: updatedLesson.attachments
        })
      })

      if (response.ok) {
        const result = await response.json()
        const updatedModules = course.modules.map(m =>
          m.id === moduleId
            ? {
                ...m,
                lessons: m.lessons.map(l =>
                  l.id === lesson.id ? result.lesson : l
                )
              }
            : m
        )
        setCourse({ ...course, modules: updatedModules })
        setIsEditing(false)
      }
    } catch (error) {
      console.error('Failed to update lesson:', error)
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'VIDEO':
        return <Play className="h-4 w-4 text-red-600" />
      case 'QUIZ':
        return <HelpCircle className="h-4 w-4 text-purple-600" />
      default:
        return <BookOpen className="h-4 w-4 text-blue-600" />
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'VIDEO':
        return 'bg-red-100 text-red-800'
      case 'QUIZ':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-blue-100 text-blue-800'
    }
  }

  const deleteLesson = async () => {
    if (!confirm('Are you sure you want to delete this lesson?')) return

    try {
      const response = await fetch(`/api/lessons/${lesson.id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        const updatedModules = course.modules.map(m =>
          m.id === moduleId
            ? { ...m, lessons: m.lessons.filter(l => l.id !== lesson.id) }
            : m
        )
        setCourse({ ...course, modules: updatedModules })
      }
    } catch (error) {
      console.error('Failed to delete lesson:', error)
    }
  }

  return (
    <>
      <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-full ${getTypeColor(lesson.type).split(' ')[0]}`}>
            {getTypeIcon(lesson.type)}
          </div>
          <div>
            <div className="font-medium text-gray-900">
              {index + 1}. {lesson.title}
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <span className={`px-2 py-1 rounded-full text-xs ${getTypeColor(lesson.type)}`}>
                {lesson.type.toLowerCase()}
              </span>
              {lesson.videoUrl && (
                <span className="text-xs text-blue-600">• Video attached</span>
              )}
              {lesson.attachments && lesson.attachments.length > 0 && (
                <span className="text-xs text-green-600">• {lesson.attachments.length} attachments</span>
              )}
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button size="sm" variant="ghost" onClick={() => setIsEditing(true)}>
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={deleteLesson}
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <LessonEditor
        lesson={lesson}
        onSave={updateLesson}
        onCancel={() => setIsEditing(false)}
        isOpen={isEditing}
      />
    </>
  )
}
